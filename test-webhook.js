// Test script to simulate a GitHub installation webhook
const crypto = require('crypto');
require('dotenv').config({ path: '.env.local' });

// Mock GitHub installation webhook payload
const mockPayload = {
  action: 'created',
  installation: {
    id: ********,
    account: {
      id: ********,
      login: '<PERSON><PERSON><PERSON>tai<PERSON>',
      type: 'User'
    }
  },
  sender: {
    id: ********,
    login: 'Mr<PERSON>eritaite'
  }
};

function createWebhookSignature(payload, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(payload);
  return 'sha256=' + hmac.digest('hex');
}

async function testWebhook() {
  try {
    const payloadString = JSON.stringify(mockPayload);
    const signature = createWebhookSignature(payloadString, process.env.GITHUB_WEBHOOK_SECRET);
    
    console.log('Testing webhook with payload:');
    console.log(JSON.stringify(mockPayload, null, 2));
    console.log('\nSending webhook to http://localhost:3001/api/webhooks/github');
    
    const response = await fetch('http://localhost:3001/api/webhooks/github', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-GitHub-Event': 'installation',
        'X-Hub-Signature-256': signature,
        'User-Agent': 'GitHub-Hookshot/test'
      },
      body: payloadString
    });
    
    const result = await response.json();
    
    console.log('\nWebhook response status:', response.status);
    console.log('Webhook response:', result);
    
    if (response.status === 200 && result.success) {
      console.log('✅ Webhook processed successfully');
      console.log('Check the server logs to see if organization ID was extracted correctly');
    } else {
      console.log('❌ Webhook processing failed');
    }
    
  } catch (error) {
    console.error('❌ Error testing webhook:', error);
  }
}

testWebhook();
